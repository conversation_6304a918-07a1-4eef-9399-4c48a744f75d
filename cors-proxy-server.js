// 简单的本地 CORS 代理服务器
// 用于解决 Qwen3-32B API 的跨域问题
// 使用方法: node cors-proxy-server.js

const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 8080;

// 创建代理服务器
const server = http.createServer((req, res) => {
    // 设置 CORS 头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    
    // 处理预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // 解析请求URL
    const reqUrl = req.url;
    if (!reqUrl || reqUrl === '/') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
            <h1>🔧 Qwen3-32B CORS 代理服务器</h1>
            <p>服务器运行在端口 ${PORT}</p>
            <p>使用方法: http://localhost:${PORT}/目标URL</p>
            <p>例如: http://localhost:${PORT}/https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions</p>
            <hr>
            <p>状态: <span style="color: green;">✅ 运行中</span></p>
        `);
        return;
    }
    
    // 提取目标URL
    let targetUrl;
    try {
        // 移除开头的斜杠
        const cleanUrl = reqUrl.startsWith('/') ? reqUrl.slice(1) : reqUrl;
        targetUrl = new URL(cleanUrl);
    } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: '无效的目标URL', details: error.message }));
        return;
    }
    
    console.log(`🔄 代理请求: ${req.method} ${targetUrl.href}`);
    
    // 准备代理请求选项
    const options = {
        hostname: targetUrl.hostname,
        port: targetUrl.port || (targetUrl.protocol === 'https:' ? 443 : 80),
        path: targetUrl.pathname + targetUrl.search,
        method: req.method,
        headers: {}
    };
    
    // 复制请求头，但排除一些可能导致问题的头
    const excludeHeaders = ['host', 'origin', 'referer'];
    for (const [key, value] of Object.entries(req.headers)) {
        if (!excludeHeaders.includes(key.toLowerCase())) {
            options.headers[key] = value;
        }
    }
    
    // 选择 HTTP 或 HTTPS 模块
    const httpModule = targetUrl.protocol === 'https:' ? https : http;
    
    // 创建代理请求
    const proxyReq = httpModule.request(options, (proxyRes) => {
        console.log(`📡 响应状态: ${proxyRes.statusCode}`);
        
        // 设置响应头
        res.writeHead(proxyRes.statusCode, proxyRes.headers);
        
        // 转发响应数据
        proxyRes.pipe(res);
    });
    
    // 处理代理请求错误
    proxyReq.on('error', (error) => {
        console.error('❌ 代理请求错误:', error.message);
        
        if (!res.headersSent) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ 
                error: '代理请求失败', 
                details: error.message,
                target: targetUrl.href
            }));
        }
    });
    
    // 处理客户端请求错误
    req.on('error', (error) => {
        console.error('❌ 客户端请求错误:', error.message);
        proxyReq.destroy();
    });
    
    // 转发请求体
    req.pipe(proxyReq);
});

// 处理服务器错误
server.on('error', (error) => {
    console.error('❌ 服务器错误:', error.message);
});

// 启动服务器
server.listen(PORT, () => {
    console.log(`🚀 CORS 代理服务器已启动`);
    console.log(`📡 监听端口: ${PORT}`);
    console.log(`🌐 访问地址: http://localhost:${PORT}`);
    console.log(`💡 使用方法: http://localhost:${PORT}/目标URL`);
    console.log(`📝 示例: http://localhost:${PORT}/https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions`);
    console.log('');
    console.log('按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 收到终止信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});
