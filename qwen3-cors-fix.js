// Qwen3-32B CORS 问题专用修复工具
// 这是一个简化但更有效的解决方案

class Qwen3CorsHelper {
    constructor() {
        this.apiKey = '';
        this.baseUrl = 'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1';
        
        // 简化的代理列表，只保留最可靠的
        this.proxies = [
            {
                name: '直接请求',
                url: '',
                test: () => this.testDirect()
            },
            {
                name: 'CORS Anywhere',
                url: 'https://cors-anywhere.herokuapp.com/',
                test: () => this.testCorsAnywhere()
            },
            {
                name: 'AllOrigins',
                url: 'https://api.allorigins.win/raw?url=',
                test: () => this.testAllOrigins()
            },
            {
                name: '本地代理',
                url: 'http://localhost:8080/',
                test: () => this.testLocalProxy()
            }
        ];
        
        this.workingProxy = null;
    }
    
    setApiKey(apiKey) {
        this.apiKey = apiKey;
    }
    
    // 测试直接请求
    async testDirect() {
        try {
            const response = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'qwen3:32b',
                    messages: [{ role: 'user', content: 'test' }],
                    max_tokens: 1
                }),
                mode: 'cors'
            });
            return response.ok;
        } catch (error) {
            console.log('直接请求失败:', error.message);
            return false;
        }
    }
    
    // 测试 CORS Anywhere
    async testCorsAnywhere() {
        try {
            const proxyUrl = 'https://cors-anywhere.herokuapp.com/' + this.baseUrl + '/chat/completions';
            const response = await fetch(proxyUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    model: 'qwen3:32b',
                    messages: [{ role: 'user', content: 'test' }],
                    max_tokens: 1
                })
            });
            return response.ok;
        } catch (error) {
            console.log('CORS Anywhere 失败:', error.message);
            return false;
        }
    }
    
    // 测试 AllOrigins
    async testAllOrigins() {
        try {
            const targetUrl = `${this.baseUrl}/chat/completions`;
            const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(targetUrl)}`;
            
            const response = await fetch(proxyUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'qwen3:32b',
                        messages: [{ role: 'user', content: 'test' }],
                        max_tokens: 1
                    })
                })
            });
            return response.ok;
        } catch (error) {
            console.log('AllOrigins 失败:', error.message);
            return false;
        }
    }
    
    // 测试本地代理
    async testLocalProxy() {
        try {
            const proxyUrl = 'http://localhost:8080/' + this.baseUrl + '/chat/completions';
            const response = await fetch(proxyUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: 'qwen3:32b',
                    messages: [{ role: 'user', content: 'test' }],
                    max_tokens: 1
                })
            });
            return response.ok;
        } catch (error) {
            console.log('本地代理失败:', error.message);
            return false;
        }
    }
    
    // 自动检测可用的代理
    async detectWorkingProxy() {
        console.log('🔍 开始检测可用的代理...');
        
        for (const proxy of this.proxies) {
            console.log(`🧪 测试 ${proxy.name}...`);
            
            try {
                const isWorking = await proxy.test();
                if (isWorking) {
                    console.log(`✅ ${proxy.name} 可用!`);
                    this.workingProxy = proxy;
                    return proxy;
                }
            } catch (error) {
                console.log(`❌ ${proxy.name} 测试失败:`, error.message);
            }
        }
        
        console.log('❌ 没有找到可用的代理');
        return null;
    }
    
    // 使用检测到的代理发送请求
    async sendRequest(messages, options = {}) {
        if (!this.workingProxy) {
            await this.detectWorkingProxy();
        }
        
        if (!this.workingProxy) {
            throw new Error('没有可用的代理服务');
        }
        
        const requestData = {
            model: 'qwen3:32b',
            messages: messages,
            max_tokens: options.max_tokens || 2000,
            temperature: options.temperature || 0.6,
            stream: false
        };
        
        console.log(`🚀 使用 ${this.workingProxy.name} 发送请求...`);
        
        if (this.workingProxy.name === '直接请求') {
            return await this.sendDirectRequest(requestData);
        } else if (this.workingProxy.name === 'CORS Anywhere') {
            return await this.sendCorsAnywhereRequest(requestData);
        } else if (this.workingProxy.name === 'AllOrigins') {
            return await this.sendAllOriginsRequest(requestData);
        } else if (this.workingProxy.name === '本地代理') {
            return await this.sendLocalProxyRequest(requestData);
        }
        
        throw new Error('未知的代理类型');
    }
    
    async sendDirectRequest(requestData) {
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData),
            mode: 'cors'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${await response.text()}`);
        }
        
        return await response.json();
    }
    
    async sendCorsAnywhereRequest(requestData) {
        const proxyUrl = 'https://cors-anywhere.herokuapp.com/' + this.baseUrl + '/chat/completions';
        const response = await fetch(proxyUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${await response.text()}`);
        }
        
        return await response.json();
    }
    
    async sendAllOriginsRequest(requestData) {
        // AllOrigins 需要特殊处理
        const targetUrl = `${this.baseUrl}/chat/completions`;
        const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(targetUrl)}`;
        
        const response = await fetch(proxyUrl, {
            method: 'GET'
        });
        
        if (!response.ok) {
            throw new Error(`AllOrigins HTTP ${response.status}: ${await response.text()}`);
        }
        
        const data = await response.json();
        return JSON.parse(data.contents);
    }
    
    async sendLocalProxyRequest(requestData) {
        const proxyUrl = 'http://localhost:8080/' + this.baseUrl + '/chat/completions';
        const response = await fetch(proxyUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${await response.text()}`);
        }
        
        return await response.json();
    }
    
    // 获取状态信息
    getStatus() {
        return {
            hasApiKey: !!this.apiKey,
            workingProxy: this.workingProxy?.name || '未检测',
            isReady: !!(this.apiKey && this.workingProxy)
        };
    }
}

// 导出到全局
window.Qwen3CorsHelper = Qwen3CorsHelper;
