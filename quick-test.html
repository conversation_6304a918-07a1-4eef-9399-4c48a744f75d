<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qwen3-32B 快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Qwen3-32B API 快速测试</h1>
        <p>这个页面用于快速测试 Qwen3-32B API 的修复效果。</p>
        
        <div class="input-group">
            <label for="apiKey">API Key:</label>
            <input type="password" id="apiKey" placeholder="输入您的 Qwen3-32B API 密钥" value="0621faa5641beacc5940d0fb978114a03a69b7eb">
        </div>
        
        <div class="input-group">
            <label for="message">测试消息:</label>
            <textarea id="message" rows="3" placeholder="输入要测试的消息">你好，请简单介绍一下你自己</textarea>
        </div>
        
        <button onclick="testDirectCall()">直接调用测试</button>
        <button onclick="testWithProxy()">Qwen3 专用助手测试</button>
        <button onclick="testAllMethods()">全面测试</button>
        
        <div id="result"></div>
    </div>

    <script src="ai-utils.js"></script>
    <script src="qwen3-cors-fix.js"></script>
    <script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function getApiKey() {
            return document.getElementById('apiKey').value.trim();
        }
        
        function getMessage() {
            return document.getElementById('message').value.trim();
        }
        
        async function testDirectCall() {
            const apiKey = getApiKey();
            const message = getMessage();
            
            if (!apiKey || !message) {
                showResult('请输入API密钥和测试消息！', 'error');
                return;
            }
            
            showResult('🌐 正在测试直接调用...', 'loading');
            
            try {
                const response = await fetch('https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'qwen3:32b',
                        messages: [{ role: 'user', content: message }],
                        max_tokens: 100,
                        temperature: 0.6
                    }),
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✅ 直接调用成功！\n\n响应: ${data.choices[0].message.content}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`❌ 直接调用失败: ${response.status} ${response.statusText}\n\n错误详情: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 直接调用错误: ${error.message}\n\n这通常是CORS问题，需要使用代理。`, 'error');
            }
        }
        
        async function testWithProxy() {
            const apiKey = getApiKey();
            const message = getMessage();

            if (!apiKey || !message) {
                showResult('请输入API密钥和测试消息！', 'error');
                return;
            }

            showResult('🔄 正在测试 Qwen3 专用助手...', 'loading');

            try {
                const qwen3Helper = new Qwen3CorsHelper();
                qwen3Helper.setApiKey(apiKey);

                // 检测可用的代理
                const workingProxy = await qwen3Helper.detectWorkingProxy();

                if (workingProxy) {
                    // 发送测试消息
                    const messages = [{ role: 'user', content: message }];
                    const data = await qwen3Helper.sendRequest(messages, { max_tokens: 100 });

                    showResult(
                        `✅ Qwen3 专用助手调用成功！\n\n` +
                        `🔗 使用代理: ${workingProxy.name}\n` +
                        `📝 响应: ${data.choices[0].message.content}`,
                        'success'
                    );
                } else {
                    showResult(`❌ Qwen3 专用助手调用失败: 没有找到可用的代理`, 'error');
                }
            } catch (error) {
                showResult(`❌ Qwen3 专用助手调用错误: ${error.message}`, 'error');
            }
        }
        
        async function testAllMethods() {
            const apiKey = getApiKey();
            const message = getMessage();
            
            if (!apiKey || !message) {
                showResult('请输入API密钥和测试消息！', 'error');
                return;
            }
            
            showResult('🧪 正在进行全面测试...', 'loading');
            
            let results = [];
            
            // 测试1: 直接调用
            try {
                const response = await fetch('https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'qwen3:32b',
                        messages: [{ role: 'user', content: message }],
                        max_tokens: 50
                    }),
                    mode: 'cors'
                });
                
                if (response.ok) {
                    results.push('✅ 直接调用: 成功');
                } else {
                    results.push(`❌ 直接调用: 失败 (${response.status})`);
                }
            } catch (error) {
                results.push(`❌ 直接调用: CORS错误 - ${error.message}`);
            }
            
            // 测试2: Qwen3 专用助手
            try {
                const qwen3Helper = new Qwen3CorsHelper();
                qwen3Helper.setApiKey(apiKey);

                const workingProxy = await qwen3Helper.detectWorkingProxy();

                if (workingProxy) {
                    const messages = [{ role: 'user', content: message }];
                    const data = await qwen3Helper.sendRequest(messages, { max_tokens: 50 });

                    results.push(`✅ Qwen3 专用助手: 成功 (${workingProxy.name})`);
                    results.push(`📝 响应内容: ${data.choices[0].message.content}`);
                } else {
                    results.push(`❌ Qwen3 专用助手: 没有可用代理`);
                }
            } catch (error) {
                results.push(`❌ Qwen3 专用助手: 错误 - ${error.message}`);
            }
            
            // 测试3: 网络连接
            try {
                const corsProxy = new CorsProxy();
                const isOnline = await corsProxy.testConnection();
                results.push(`🌐 网络状态: ${isOnline ? '在线' : '离线'}`);
            } catch (error) {
                results.push(`🌐 网络状态: 检测失败 - ${error.message}`);
            }
            
            const finalResult = results.join('\n\n');
            const hasSuccess = results.some(r => r.includes('✅'));
            showResult(`📊 全面测试完成:\n\n${finalResult}`, hasSuccess ? 'success' : 'error');
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 测试页面已准备就绪，请点击按钮开始测试', 'info');
        });
    </script>
</body>
</html>
