@echo off
echo 🚀 启动 Qwen3-32B CORS 代理服务器
echo.
echo 正在检查 Node.js...

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Node.js
    echo 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
echo.
echo 🔧 启动代理服务器...
echo 服务器将运行在 http://localhost:8080
echo.
echo 💡 使用说明:
echo 1. 保持此窗口打开
echo 2. 在浏览器中打开 ai-chat.html
echo 3. 配置 Qwen3-32B API 密钥
echo 4. 选择 Qwen3-32B 模型
echo 5. 开始聊天
echo.
echo 按 Ctrl+C 停止服务器
echo.

node cors-proxy-server.js

pause
